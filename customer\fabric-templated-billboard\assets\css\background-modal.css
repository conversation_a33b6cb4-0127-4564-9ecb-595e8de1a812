/* ========================================
   BACKGROUND MODAL AND SELECTION
   ======================================== */

/* Background Change Container */
.background-change-container {
    background: #f8f9fa;
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    border: 1px solid #e0e0e0;
}

.background-controls {
    text-align: center;
}

/* Background Modal Styles */
.background-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(108, 117, 125, 0.5);
}

.modal-content {
    position: relative;
    background: white;
    border-radius: var(--border-radius);
    max-width: 90vw;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(108, 117, 125, 0.3);
}

/* Desktop Modal Content - Larger for grid display */
@media (min-width: 1024px) {
    .modal-content {
        max-width: 95vw;
        max-height: 90vh;
        width: 1200px;
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    border-bottom: 1px solid #e0e0e0;
    background: #f8f9fa;
}

.modal-header h3 {
    margin: 0;
    color: #495057;
}

.modal-body {
    padding: var(--spacing-md);
    max-height: 60vh;
    overflow-y: auto;
}

/* Desktop Modal Body - More space for grid */
@media (min-width: 1024px) {
    .modal-body {
        max-height: 75vh;
        padding: var(--spacing-lg);
    }
}

.background-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: var(--spacing-sm);
}

/* Desktop Grid Layout - More columns and larger images */
@media (min-width: 1024px) {
    .background-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: var(--spacing-md);
        padding: 0; /* Remove padding to eliminate white space */
    }
}

.background-option {
    aspect-ratio: 1;
    background-size: cover; /* Changed back to cover to fill container without white space */
    background-position: center;
    background-repeat: no-repeat;
    border: 2px solid #e0e0e0;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
    min-height: 120px;
    overflow: hidden; /* Ensure no overflow */
}

/* Desktop Background Options - Larger display */
@media (min-width: 1024px) {
    .background-option {
        min-height: 200px;
        background-size: cover; /* Fill the container completely */
    }
}

.background-option:hover {
    border-color: var(--primary-color);
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.background-option:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Selected state for background options */
.background-option.selected {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.2);
}

/* Desktop hover effects */
@media (min-width: 1024px) {
    .background-option:hover {
        transform: scale(1.03);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    }
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6c757d;
    padding: 4px;
    line-height: 1;
}

.close-btn:hover {
    color: #495057;
}

/* Loading and Empty States */
.background-grid-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
    color: #6c757d;
}

.background-grid-empty {
    text-align: center;
    padding: var(--spacing-xl);
    color: #6c757d;
}

.background-grid-empty i {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
}

/* Image loading placeholder */
.background-option.loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}
