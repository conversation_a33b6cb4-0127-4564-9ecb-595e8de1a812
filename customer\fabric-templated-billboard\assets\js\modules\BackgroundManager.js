/**
 * BackgroundManager - Handles background image selection and replacement
 * Follows single responsibility principle - only manages background changes
 */
class BackgroundManager {
    constructor(canvasManager, templateManager) {
        this.canvasManager = canvasManager;
        this.templateManager = templateManager;
        this.modal = null;
        this.currentCategory = null;
        this.init();
    }

    /**
     * Initialize background manager
     */
    init() {
        this.modal = document.getElementById('backgroundModal');
        this.currentBackgroundType = 'images'; // 'images' or 'colors'
        this.bindEvents();
        this.initializeColorPicker();
    }

    /**
     * Bind event handlers
     */
    bindEvents() {
        // Change background button
        const changeBtn = document.getElementById('changeBackgroundBtn');
        if (changeBtn) {
            changeBtn.addEventListener('click', () => this.showBackgroundModal());
        }

        // Close modal button
        const closeBtn = document.getElementById('closeBackgroundModal');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => this.hideBackgroundModal());
        }

        // Modal overlay click to close
        if (this.modal) {
            const overlay = this.modal.querySelector('.modal-overlay');
            if (overlay) {
                overlay.addEventListener('click', () => this.hideBackgroundModal());
            }
        }

        // Background type selector buttons
        const typeBtns = document.querySelectorAll('.type-btn');
        typeBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const type = e.currentTarget.dataset.type;
                this.switchBackgroundType(type);
            });
        });

        console.log('✅ BackgroundManager events bound');
    }

    /**
     * Show background selection modal
     */
    showBackgroundModal() {
        if (!this.currentCategory) {
            this.showError('Please load a template first to change background');
            return;
        }

        this.populateBackgroundGrid();

        if (this.modal) {
            this.modal.style.display = 'flex';
            // Add animation class after display
            setTimeout(() => {
                this.modal.classList.add('show');
            }, 10);
        }

        console.log('🎨 Background modal opened');
    }

    /**
     * Hide background selection modal
     */
    hideBackgroundModal() {
        if (this.modal) {
            this.modal.classList.remove('show');
            setTimeout(() => {
                this.modal.style.display = 'none';
            }, 200);
        }
    }

    /**
     * Populate background grid with available backgrounds
     */
    populateBackgroundGrid() {
        const grid = document.getElementById('backgroundGrid');
        if (!grid) return;

        // Show loading state
        grid.innerHTML = '<div class="background-grid-loading"><i class="fas fa-spinner fa-spin"></i> Loading backgrounds...</div>';

        // Get backgrounds for current category
        const backgrounds = this.getBackgroundsForCategory(this.currentCategory);

        // Clear loading state
        grid.innerHTML = '';

        if (!backgrounds || backgrounds.length === 0) {
            grid.innerHTML = `
                <div class="background-grid-empty">
                    <i class="fas fa-image"></i>
                    <p>No background images available for this category.</p>
                </div>
            `;
            return;
        }

        backgrounds.forEach((bg) => {
            const option = document.createElement('div');
            option.className = 'background-option loading';
            option.title = bg.name;
            option.setAttribute('data-bg-url', bg.url);
            option.setAttribute('tabindex', '0');
            option.setAttribute('role', 'button');
            option.setAttribute('aria-label', `Select background: ${bg.name}`);

            // Create image to preload and check if it loads successfully
            const img = new Image();
            img.onload = () => {
                option.classList.remove('loading');
                option.style.backgroundImage = `url('${bg.url}')`;
                console.log(`✅ Background image loaded: ${bg.name}`);
            };
            img.onerror = () => {
                option.classList.remove('loading');
                option.innerHTML = '<i class="fas fa-exclamation-triangle"></i><br><small>Image not found</small>';
                option.style.backgroundColor = '#f8f9fa';
                option.style.color = '#6c757d';
                option.style.display = 'flex';
                option.style.alignItems = 'center';
                option.style.justifyContent = 'center';
                option.style.flexDirection = 'column';
                option.style.fontSize = '12px';
                option.style.textAlign = 'center';
                option.style.cursor = 'not-allowed';
                console.warn(`❌ Failed to load background image: ${bg.url}`);

                // Remove click handlers for broken images
                option.removeEventListener('click', () => {});
                option.removeEventListener('keydown', () => {});
            };
            img.src = bg.url;

            // Click handler
            option.addEventListener('click', () => {
                this.selectBackground(bg.url, option);
            });

            // Keyboard handler
            option.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.selectBackground(bg.url, option);
                }
            });

            grid.appendChild(option);
        });

        console.log(`📋 Populated ${backgrounds.length} background options`);
    }

    /**
     * Select and apply background
     */
    selectBackground(imageUrl, selectedElement = null) {
        console.log('🎨 Applying background:', imageUrl);

        // Update visual selection state
        if (selectedElement) {
            // Remove previous selection
            const grid = document.getElementById('backgroundGrid');
            if (grid) {
                grid.querySelectorAll('.background-option').forEach(option => {
                    option.classList.remove('selected');
                });
            }

            // Add selection to current element
            selectedElement.classList.add('selected');
        }

        // Use CanvasManager's proper background scaling method
        this.canvasManager.setBackgroundImage(imageUrl, (img) => {
            if (img) {
                console.log('✅ Background applied successfully with proper scaling');

                // Trigger success event
                document.dispatchEvent(new CustomEvent('background:changed', {
                    detail: { success: true, message: 'Background changed successfully!' }
                }));
            } else {
                console.error('❌ Failed to apply background');

                // Trigger error event
                document.dispatchEvent(new CustomEvent('background:error', {
                    detail: { success: false, message: 'Failed to load background image' }
                }));
            }
        });

        this.hideBackgroundModal();
    }

    /**
     * Get backgrounds for category (using template-manager data)
     */
    getBackgroundsForCategory(category) {
        // Use the existing template manager's background data
        if (this.templateManager && this.templateManager.getBackgroundsForCategory) {
            const backgrounds = this.templateManager.getBackgroundsForCategory(category);
            console.log(`📋 Template manager provided ${backgrounds.length} backgrounds for ${category}`);
            return backgrounds;
        }

        // Fallback: generate backgrounds from template specs
        console.log(`⚠️ Template manager not available, using fallback for ${category}`);
        return this.generateBackgroundsFromTemplates(category);
    }

    /**
     * Generate background options from existing templates
     */
    generateBackgroundsFromTemplates(category) {
        // First try to get all available background images for the category (1-15)
        const backgrounds = this.generateAllBackgroundsForCategory(category);

        if (backgrounds.length > 0) {
            return backgrounds;
        }

        // Fallback: extract from template definitions
        const templates = window.TEMPLATE_SPECS?.[category] || {};
        const fallbackBackgrounds = [];

        Object.entries(templates).forEach(([id, template]) => {
            if (template.background) {
                fallbackBackgrounds.push({
                    id: `${category}-${id}`,
                    url: template.background,
                    name: `${category} Background ${fallbackBackgrounds.length + 1}`
                });
            }
        });

        return fallbackBackgrounds;
    }

    /**
     * Generate all available background images for a category (1-15)
     */
    generateAllBackgroundsForCategory(category) {
        const backgrounds = [];
        const basePath = '../../../../stock-image';

        // Generate paths for images 1-15
        for (let i = 1; i <= 15; i++) {
            backgrounds.push({
                id: `${category.toLowerCase()}-${i}`,
                url: `${basePath}/${category}/${category}-${i}.png`,
                name: `${category} Background ${i}`
            });
        }

        console.log(`📋 Generated ${backgrounds.length} background options for ${category}`);
        return backgrounds;
    }

    /**
     * Set current category
     */
    setCurrentCategory(category) {
        this.currentCategory = category;
        console.log('📂 Background manager category set to:', category);
    }

    /**
     * Show background change section
     */
    showBackgroundSection() {
        const section = document.getElementById('backgroundChangeSection');
        if (section) {
            section.style.display = 'block';
        }
    }

    /**
     * Hide background change section
     */
    hideBackgroundSection() {
        const section = document.getElementById('backgroundChangeSection');
        if (section) {
            section.style.display = 'none';
        }
    }

    /**
     * Show error message
     */
    showError(message) {
        document.dispatchEvent(new CustomEvent('background:error', {
            detail: { message }
        }));
    }

    /**
     * Clear background manager
     */
    clear() {
        this.currentCategory = null;
        this.hideBackgroundModal();
        this.hideBackgroundSection();
    }

    /**
     * Initialize color picker functionality
     */
    initializeColorPicker() {
        // Color preset click handlers
        const colorPresets = document.querySelectorAll('.color-preset');
        colorPresets.forEach(preset => {
            preset.addEventListener('click', (e) => {
                const color = e.currentTarget.dataset.color;
                this.selectColorBackground(color);
                this.updateColorPresetSelection(e.currentTarget);
            });
        });

        // Custom color picker
        const applyColorBtn = document.getElementById('applyCustomColor');
        if (applyColorBtn) {
            applyColorBtn.addEventListener('click', () => {
                const colorPicker = document.getElementById('colorPicker');
                if (colorPicker) {
                    this.selectColorBackground(colorPicker.value);
                    this.clearColorPresetSelection();
                }
            });
        }

        console.log('✅ Color picker initialized');
    }

    /**
     * Switch between background types (images/colors)
     */
    switchBackgroundType(type) {
        this.currentBackgroundType = type;

        // Update type buttons
        const typeBtns = document.querySelectorAll('.type-btn');
        typeBtns.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.type === type);
        });

        // Update sections
        const imagesSection = document.getElementById('backgroundImagesSection');
        const colorsSection = document.getElementById('backgroundColorsSection');

        if (type === 'images') {
            imagesSection?.classList.add('active');
            colorsSection?.classList.remove('active');
        } else {
            imagesSection?.classList.remove('active');
            colorsSection?.classList.add('active');
        }

        console.log(`🎨 Switched to ${type} background type`);
    }

    /**
     * Select color background
     */
    selectColorBackground(color) {
        if (!this.canvasManager || !this.canvasManager.canvas) {
            this.showError('Canvas not available');
            return;
        }

        try {
            // Set canvas background color
            this.canvasManager.canvas.setBackgroundColor(color, () => {
                this.canvasManager.canvas.renderAll();
            });

            console.log(`🎨 Applied color background: ${color}`);

            // Close modal after selection
            this.hideBackgroundModal();

            // Show success message
            document.dispatchEvent(new CustomEvent('background:changed', {
                detail: { type: 'color', value: color }
            }));

        } catch (error) {
            console.error('❌ Error applying color background:', error);
            this.showError('Failed to apply color background');
        }
    }

    /**
     * Update color preset selection visual state
     */
    updateColorPresetSelection(selectedPreset) {
        const colorPresets = document.querySelectorAll('.color-preset');
        colorPresets.forEach(preset => {
            preset.classList.remove('selected');
        });
        selectedPreset.classList.add('selected');
    }

    /**
     * Clear color preset selection
     */
    clearColorPresetSelection() {
        const colorPresets = document.querySelectorAll('.color-preset');
        colorPresets.forEach(preset => {
            preset.classList.remove('selected');
        });
    }
}

// Export for use in main application
window.BackgroundManager = BackgroundManager;
